using SinterOptimizationClient.ViewModels;
using SinterOptimizationClient.Services;
using System;
using System.Net.Http;
using System.Windows;

namespace SinterOptimizationClient
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            try
            {
                // 手动创建服务
                var httpClient = new HttpClient
                {
                    BaseAddress = new Uri("http://localhost:5000/"),
                    Timeout = TimeSpan.FromMinutes(5)
                };

                var optimizationService = new OptimizationService(httpClient);
                var dataService = new DataService();
                var dialogService = new DialogService();
                var connectivityTestService = new ConnectivityTestService(httpClient, optimizationService);

                // 创建ViewModels
                var mainViewModel = new MainViewModel(optimizationService, dataService, dialogService, connectivityTestService);

                DataContext = mainViewModel;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
