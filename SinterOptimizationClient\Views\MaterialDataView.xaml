<UserControl x:Class="SinterOptimizationClient.Views.MaterialDataView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1600">

    <Grid Background="{StaticResource LightGrayBrush}">
        <Grid.RowDefinitions>
            <!-- 工具栏 -->
            <RowDefinition Height="50"/>
            <!-- 物料信息表 -->
            <RowDefinition Height="*"/>
            <!-- 状态栏 -->
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="10,5" VerticalAlignment="Center">
                <Button Content="添加原料" 
                        Style="{StaticResource PrimaryButtonStyle}"
                        Command="{Binding AddMaterialCommand}"
                        Margin="0,0,10,0"/>
                <Button Content="编辑" 
                        Style="{StaticResource PrimaryButtonStyle}"
                        Command="{Binding EditMaterialCommand}"
                        Margin="0,0,10,0"/>
                <Button Content="删除" 
                        Style="{StaticResource PrimaryButtonStyle}"
                        Command="{Binding DeleteMaterialCommand}"
                        Margin="0,0,20,0"/>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="0,0,20,0"/>
                <Button Content="导入原料数据" 
                        Style="{StaticResource PrimaryButtonStyle}"
                        Command="{Binding ImportMaterialsCommand}"
                        Margin="0,0,10,0"/>
                <Button Content="导出原料数据" 
                        Style="{StaticResource PrimaryButtonStyle}"
                        Command="{Binding ExportMaterialsCommand}"
                        Margin="0,0,10,0"/>
                <Button Content="刷新" 
                        Style="{StaticResource PrimaryButtonStyle}"
                        Command="{Binding RefreshDataCommand}"
                        Margin="0,0,10,0"/>
            </StackPanel>
        </Border>

        <!-- 物料信息表 -->
        <DataGrid Grid.Row="1" 
                  ItemsSource="{Binding Materials}"
                  SelectedItem="{Binding SelectedMaterial}"
                  Style="{StaticResource DataGridStyle}"
                  ColumnHeaderStyle="{StaticResource DataGridHeaderStyle}"
                  Margin="0">
            
            <DataGrid.Columns>
                <!-- 基础成分列 -->
                <DataGridTextColumn Header="品名" Binding="{Binding Name}" Width="90"/>
                <DataGridTextColumn Header="TFe" Binding="{Binding Tfe, StringFormat=F2}" Width="65"/>
                <DataGridTextColumn Header="CaO" Binding="{Binding Cao, StringFormat=F2}" Width="65"/>
                <DataGridTextColumn Header="SiO2" Binding="{Binding Sio2, StringFormat=F2}" Width="65"/>
                <DataGridTextColumn Header="MgO" Binding="{Binding Mgo, StringFormat=F2}" Width="65"/>
                <DataGridTextColumn Header="Al2O3" Binding="{Binding Al2o3, StringFormat=F2}" Width="70"/>
                <DataGridTextColumn Header="C/S" Binding="{Binding CS, StringFormat=F2}" Width="60" IsReadOnly="True"/>
                <DataGridTextColumn Header="TiO2" Binding="{Binding Tio2, StringFormat=F2}" Width="65"/>
                <DataGridTextColumn Header="Ig" Binding="{Binding Ig, StringFormat=F2}" Width="60"/>
                <DataGridTextColumn Header="H2O" Binding="{Binding H2o, StringFormat=F2}" Width="60"/>
                
                <!-- 配比相关列 -->
                <DataGridTextColumn Header="湿配比" Binding="{Binding WetRatio, StringFormat=F1}" Width="70"/>
                <DataGridTextColumn Header="干配比" Binding="{Binding DryRatio, StringFormat=F2}" Width="70" IsReadOnly="True"/>
                <DataGridTextColumn Header="烧成量" Binding="{Binding BurnAmount, StringFormat=F2}" Width="70" IsReadOnly="True"/>

                <!-- 成本相关列 -->
                <DataGridTextColumn Header="吨矿单耗" Binding="{Binding TonConsumption, StringFormat=F0}" Width="85" IsReadOnly="True"/>
                <DataGridTextColumn Header="计划单价" Binding="{Binding PlannedPrice, StringFormat=F2}" Width="85"/>
                <DataGridTextColumn Header="单位成本" Binding="{Binding UnitCost, StringFormat=F2}" Width="85" IsReadOnly="True"/>

                <!-- 约束条件列 -->
                <DataGridTextColumn Header="最小配比" Binding="{Binding MinRatio, StringFormat=F1}" Width="85"/>
                <DataGridTextColumn Header="最大配比" Binding="{Binding MaxRatio, StringFormat=F1}" Width="85"/>
                <DataGridTextColumn Header="初始配比" Binding="{Binding InitialRatio, StringFormat=F1}" Width="85"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="{StaticResource PrimaryBrush}" Padding="10,5">
            <TextBlock Text="{Binding StatusMessage}" 
                       Foreground="White" 
                       FontSize="12" 
                       VerticalAlignment="Center"/>
        </Border>

    </Grid>
</UserControl>
