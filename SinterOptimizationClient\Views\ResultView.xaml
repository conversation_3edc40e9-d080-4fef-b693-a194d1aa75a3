<UserControl x:Class="SinterOptimizationClient.Views.ResultView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:converters="clr-namespace:SinterOptimizationClient.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="900" d:DesignWidth="1600">

    <UserControl.Resources>
        <!-- 转换器 -->
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
        <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
        <converters:PercentToHeightConverter x:Key="PercentToHeightConverter"/>
        <converters:RatioToHeightConverter x:Key="RatioToHeightConverter"/>
        <converters:RatioToWidthConverter x:Key="RatioToWidthConverter"/>

        <!-- 添加空值检查转换器 -->
        <converters:NotNullToVisibilityConverter x:Key="NotNullToVisibilityConverter"/>
        
        <!-- 基础样式 -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2563EB"/>
        <SolidColorBrush x:Key="LightGrayBrush" Color="#F8F9FA"/>
        <SolidColorBrush x:Key="BorderBrush" Color="#E5E7EB"/>
        
        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F97316"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#EA580C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="{StaticResource LightGrayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <Border Grid.Row="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,0,1" Padding="20,15">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="🎯" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="智能综合优化结果" FontSize="20" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}"/>
                </StackPanel>
                
                <!-- 算法信息展示 -->
                <Border Background="#EEF2FF" BorderBrush="#C7D2FE" BorderThickness="1" CornerRadius="6" Padding="12">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✅" FontSize="14" Margin="0,0,8,0"/>
                        <TextBlock Text="质量成本综合优化算法" FontSize="14" FontWeight="Bold" Foreground="#1E40AF" Margin="0,0,20,0"/>
                        <TextBlock Text="•" FontSize="12" Foreground="#6B7280" Margin="0,0,5,0"/>
                        <TextBlock Text="同时优化质量指标与成本控制" FontSize="12" Foreground="#374151" Margin="0,0,20,0"/>
                        <TextBlock Text="•" FontSize="12" Foreground="#6B7280" Margin="0,0,5,0"/>
                        <TextBlock Text="SQP算法，精度±0.01%" FontSize="12" Foreground="#374151"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="0.55*"/>
                <ColumnDefinition Width="0.45*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：优化结果表格 -->
            <Border Grid.Column="0" Background="White" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,1,0" Padding="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 表格标题 -->
                    <TextBlock Grid.Row="0" Text="📋 优化方案详情" FontSize="16" FontWeight="Bold" Foreground="#1E40AF" Margin="0,0,0,15"/>

                    <!-- 优化结果卡片式展示 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- 综合优化方案卡片 -->
                            <Border Background="#F8FAFC" BorderBrush="#E2E8F0" BorderThickness="1" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 方案标题 -->
                                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                                        <Border Background="#1E40AF" CornerRadius="4" Padding="8,4">
                                            <TextBlock Text="方案 1" FontSize="12" FontWeight="Bold" Foreground="White"/>
                                        </Border>
                                        <TextBlock Text="质量成本综合优化" FontSize="14" FontWeight="Bold" Foreground="#1E40AF" VerticalAlignment="Center" Margin="10,0,0,0"/>
                                    </StackPanel>

                                    <!-- 关键指标 -->
                                    <Grid Grid.Row="1" Margin="0,0,0,15">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 成本 -->
                                        <Border Grid.Column="0" Background="#FEF3C7" CornerRadius="6" Padding="10" Margin="0,0,5,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="💰" FontSize="16" HorizontalAlignment="Center"/>
                                                <TextBlock Text="成本" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#92400E" Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding SelectedSolution.Cost, StringFormat=F1, FallbackValue=650.8}" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F59E0B"/>
                                                <TextBlock Text="元/吨" FontSize="9" HorizontalAlignment="Center" Foreground="#6B7280"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- TFe -->
                                        <Border Grid.Column="1" Background="#EEF2FF" CornerRadius="6" Padding="10" Margin="2.5,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="⚡" FontSize="16" HorizontalAlignment="Center"/>
                                                <TextBlock Text="TFe" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1E40AF" Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding SelectedSolution.TFe, StringFormat=F2, FallbackValue=55.00}" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#2563EB"/>
                                                <TextBlock Text="%" FontSize="9" HorizontalAlignment="Center" Foreground="#6B7280"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- R -->
                                        <Border Grid.Column="2" Background="#F0FDF4" CornerRadius="6" Padding="10" Margin="2.5,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="⚖️" FontSize="16" HorizontalAlignment="Center"/>
                                                <TextBlock Text="碱度R" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#15803D" Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding SelectedSolution.R, StringFormat=F3, FallbackValue=1.903}" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#22C55E"/>
                                                <TextBlock Text="" FontSize="9" HorizontalAlignment="Center" Foreground="#6B7280"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- MgO -->
                                        <Border Grid.Column="3" Background="#FDF2F8" CornerRadius="6" Padding="10" Margin="2.5,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="🔬" FontSize="16" HorizontalAlignment="Center"/>
                                                <TextBlock Text="MgO" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#BE185D" Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding SelectedSolution.MgO, StringFormat=F2, FallbackValue=2.41}" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#EC4899"/>
                                                <TextBlock Text="%" FontSize="9" HorizontalAlignment="Center" Foreground="#6B7280"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Al2O3 -->
                                        <Border Grid.Column="4" Background="#F3E8FF" CornerRadius="6" Padding="10" Margin="5,0,0,0">
                                            <StackPanel HorizontalAlignment="Center">
                                                <TextBlock Text="🧪" FontSize="16" HorizontalAlignment="Center"/>
                                                <TextBlock Text="Al₂O₃" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#7C3AED" Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding SelectedSolution.Al2O3, StringFormat=F2, FallbackValue=2.34}" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#8B5CF6"/>
                                                <TextBlock Text="%" FontSize="9" HorizontalAlignment="Center" Foreground="#6B7280"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>

                                    <!-- 物料配比展示 -->
                                    <StackPanel Grid.Row="2" Margin="0,0,0,15">
                                        <TextBlock Text="📦 主要物料配比" FontSize="13" FontWeight="Bold" Foreground="#374151" Margin="0,0,0,10"/>
                                        
                                        <!-- 配比条形图 -->
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="60"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 高炉返矿 -->
                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="高炉返矿" FontSize="11" VerticalAlignment="Center" Foreground="#374151"/>
                                            <Border Grid.Row="0" Grid.Column="1" Background="#E5E7EB" Height="16" CornerRadius="8" Margin="5,2">
                                                <Border Background="#06B6D4" Height="16" CornerRadius="8" HorizontalAlignment="Left" Width="106"/>
                                            </Border>
                                            <TextBlock Grid.Row="0" Grid.Column="2" Text="26.6%" FontSize="11" VerticalAlignment="Center" FontWeight="Bold" Foreground="#06B6D4"/>

                                            <!-- 印粉海娜 -->
                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="印粉海娜" FontSize="11" VerticalAlignment="Center" Foreground="#374151" Margin="0,3,0,0"/>
                                            <Border Grid.Row="1" Grid.Column="1" Background="#E5E7EB" Height="16" CornerRadius="8" Margin="5,5,5,2">
                                                <Border Background="#6366F1" Height="16" CornerRadius="8" HorizontalAlignment="Left" Width="95"/>
                                            </Border>
                                            <TextBlock Grid.Row="1" Grid.Column="2" Text="23.8%" FontSize="11" VerticalAlignment="Center" FontWeight="Bold" Foreground="#6366F1" Margin="0,3,0,0"/>

                                            <!-- 俄罗斯精粉 -->
                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="俄罗斯精粉" FontSize="11" VerticalAlignment="Center" Foreground="#374151" Margin="0,3,0,0"/>
                                            <Border Grid.Row="2" Grid.Column="1" Background="#E5E7EB" Height="16" CornerRadius="8" Margin="5,5,5,2">
                                                <Border Background="#8B5CF6" Height="16" CornerRadius="8" HorizontalAlignment="Left" Width="61"/>
                                            </Border>
                                            <TextBlock Grid.Row="2" Grid.Column="2" Text="15.3%" FontSize="11" VerticalAlignment="Center" FontWeight="Bold" Foreground="#8B5CF6" Margin="0,3,0,0"/>

                                            <!-- 澳粉纵横 -->
                                            <TextBlock Grid.Row="3" Grid.Column="0" Text="澳粉纵横" FontSize="11" VerticalAlignment="Center" Foreground="#374151" Margin="0,3,0,0"/>
                                            <Border Grid.Row="3" Grid.Column="1" Background="#E5E7EB" Height="16" CornerRadius="8" Margin="5,5,5,2">
                                                <Border Background="#F59E0B" Height="16" CornerRadius="8" HorizontalAlignment="Left" Width="44"/>
                                            </Border>
                                            <TextBlock Grid.Row="3" Grid.Column="2" Text="11.0%" FontSize="11" VerticalAlignment="Center" FontWeight="Bold" Foreground="#F59E0B" Margin="0,3,0,0"/>

                                            <!-- 回收料 -->
                                            <TextBlock Grid.Row="4" Grid.Column="0" Text="回收料" FontSize="11" VerticalAlignment="Center" Foreground="#374151" Margin="0,3,0,0"/>
                                            <Border Grid.Row="4" Grid.Column="1" Background="#E5E7EB" Height="16" CornerRadius="8" Margin="5,5,5,2">
                                                <Border Background="#10B981" Height="16" CornerRadius="8" HorizontalAlignment="Left" Width="40"/>
                                            </Border>
                                            <TextBlock Grid.Row="4" Grid.Column="2" Text="10.0%" FontSize="11" VerticalAlignment="Center" FontWeight="Bold" Foreground="#10B981" Margin="0,3,0,0"/>

                                            <!-- 其他物料 -->
                                            <TextBlock Grid.Row="5" Grid.Column="0" Text="其他物料" FontSize="11" VerticalAlignment="Center" Foreground="#374151" Margin="0,3,0,0"/>
                                            <Border Grid.Row="5" Grid.Column="1" Background="#E5E7EB" Height="16" CornerRadius="8" Margin="5,5,5,2">
                                                <Border Background="#6B7280" Height="16" CornerRadius="8" HorizontalAlignment="Left" Width="54"/>
                                            </Border>
                                            <TextBlock Grid.Row="5" Grid.Column="2" Text="13.3%" FontSize="11" VerticalAlignment="Center" FontWeight="Bold" Foreground="#6B7280" Margin="0,3,0,0"/>
                                        </Grid>
                                    </StackPanel>

                                    <!-- 优化信息 -->
                                    <Border Grid.Row="3" Background="#F1F5F9" CornerRadius="6" Padding="12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="🔄 迭代次数" FontSize="10" FontWeight="Bold" Foreground="#475569"/>
                                                <TextBlock Text="{Binding SelectedSolution.Iterations, FallbackValue=342}" FontSize="12" FontWeight="Bold" Foreground="#1E40AF" Margin="0,2,0,0"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="⏱️ 计算时间" FontSize="10" FontWeight="Bold" Foreground="#475569"/>
                                                <TextBlock Text="1.2秒" FontSize="12" FontWeight="Bold" Foreground="#1E40AF" Margin="0,2,0,0"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="2">
                                                <TextBlock Text="✅ 收敛状态" FontSize="10" FontWeight="Bold" Foreground="#475569"/>
                                                <TextBlock Text="成功收敛" FontSize="12" FontWeight="Bold" Foreground="#22C55E" Margin="0,2,0,0"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- 状态栏 -->
                    <Border Grid.Row="2" Background="{StaticResource PrimaryBrush}" CornerRadius="6" Padding="12,8">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding StatusMessage, FallbackValue=优化计算完成，共生成1个综合优化方案}" Foreground="White" FontSize="12"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- 右侧：可视化分析 -->
            <Border Grid.Column="1" Background="#F9FAFB" Padding="20">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 标题 -->
                        <TextBlock Text="📈 可视化分析" FontSize="16" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,20"/>

                        <!-- 综合评分卡片 -->
                        <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="🎯 综合评分" FontSize="14" FontWeight="Bold" Foreground="#1E40AF" Margin="0,0,0,15"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <!-- 质量评分 -->
                                    <Border Grid.Column="0" Background="#DCFCE7" CornerRadius="6" Padding="15" Margin="0,0,5,0">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🏆" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="质量评分" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#15803D"/>
                                            <TextBlock Text="99.2" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#22C55E" Margin="0,5,0,0"/>
                                            <TextBlock Text="分" FontSize="11" HorizontalAlignment="Center" Foreground="#6B7280"/>
                                        </StackPanel>
                                    </Border>
                                    
                                    <!-- 成本评分 -->
                                    <Border Grid.Column="1" Background="#FEF3C7" CornerRadius="6" Padding="15" Margin="5,0,0,0">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="💎" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                            <TextBlock Text="成本评分" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#92400E"/>
                                            <TextBlock Text="95.7" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F59E0B" Margin="0,5,0,0"/>
                                            <TextBlock Text="分" FontSize="11" HorizontalAlignment="Center" Foreground="#6B7280"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- 化学成分雷达图 -->
                        <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="📊 化学成分达标分析" FontSize="14" FontWeight="Bold" Foreground="#1E40AF" Margin="0,0,0,15"/>
                                
                                <!-- 成分达标度条形图 -->
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="60"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="50"/>
                                        <ColumnDefinition Width="50"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- TFe -->
                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="TFe" FontSize="11" FontWeight="Bold" VerticalAlignment="Center" Foreground="#1E40AF"/>
                                    <Border Grid.Row="0" Grid.Column="1" Background="#E5E7EB" Height="20" CornerRadius="10" Margin="8,2">
                                        <Border Background="#2563EB" Height="20" CornerRadius="10" HorizontalAlignment="Left" Width="95%"/>
                                    </Border>
                                    <TextBlock Grid.Row="0" Grid.Column="2" Text="55.00%" FontSize="10" VerticalAlignment="Center" Foreground="#374151"/>
                                    <TextBlock Grid.Row="0" Grid.Column="3" Text="99.8%" FontSize="10" VerticalAlignment="Center" FontWeight="Bold" Foreground="#22C55E"/>

                                    <!-- R -->
                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="碱度R" FontSize="11" FontWeight="Bold" VerticalAlignment="Center" Foreground="#15803D" Margin="0,5,0,0"/>
                                    <Border Grid.Row="1" Grid.Column="1" Background="#E5E7EB" Height="20" CornerRadius="10" Margin="8,7,8,2">
                                        <Border Background="#22C55E" Height="20" CornerRadius="10" HorizontalAlignment="Left" Width="99%"/>
                                    </Border>
                                    <TextBlock Grid.Row="1" Grid.Column="2" Text="1.903" FontSize="10" VerticalAlignment="Center" Foreground="#374151" Margin="0,5,0,0"/>
                                    <TextBlock Grid.Row="1" Grid.Column="3" Text="99.8%" FontSize="10" VerticalAlignment="Center" FontWeight="Bold" Foreground="#22C55E" Margin="0,5,0,0"/>

                                    <!-- MgO -->
                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="MgO" FontSize="11" FontWeight="Bold" VerticalAlignment="Center" Foreground="#BE185D" Margin="0,5,0,0"/>
                                    <Border Grid.Row="2" Grid.Column="1" Background="#E5E7EB" Height="20" CornerRadius="10" Margin="8,7,8,2">
                                        <Border Background="#EC4899" Height="20" CornerRadius="10" HorizontalAlignment="Left" Width="99%"/>
                                    </Border>
                                    <TextBlock Grid.Row="2" Grid.Column="2" Text="2.41%" FontSize="10" VerticalAlignment="Center" Foreground="#374151" Margin="0,5,0,0"/>
                                    <TextBlock Grid.Row="2" Grid.Column="3" Text="99.1%" FontSize="10" VerticalAlignment="Center" FontWeight="Bold" Foreground="#22C55E" Margin="0,5,0,0"/>

                                    <!-- Al2O3 -->
                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="Al₂O₃" FontSize="11" FontWeight="Bold" VerticalAlignment="Center" Foreground="#7C3AED" Margin="0,5,0,0"/>
                                    <Border Grid.Row="3" Grid.Column="1" Background="#E5E7EB" Height="20" CornerRadius="10" Margin="8,7,8,2">
                                        <Border Background="#8B5CF6" Height="20" CornerRadius="10" HorizontalAlignment="Left" Width="76%"/>
                                    </Border>
                                    <TextBlock Grid.Row="3" Grid.Column="2" Text="2.34%" FontSize="10" VerticalAlignment="Center" Foreground="#374151" Margin="0,5,0,0"/>
                                    <TextBlock Grid.Row="3" Grid.Column="3" Text="76.4%" FontSize="10" VerticalAlignment="Center" FontWeight="Bold" Foreground="#F59E0B" Margin="0,5,0,0"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- 成本构成分析 -->
                        <Border Background="White" CornerRadius="8" Padding="15" Margin="0,0,0,15">
                            <StackPanel>
                                <TextBlock Text="💰 成本构成分析" FontSize="14" FontWeight="Bold" Foreground="#1E40AF" Margin="0,0,0,15"/>
                                
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 总成本 -->
                                    <Border Grid.Row="0" Grid.ColumnSpan="2" Background="#FEF3C7" CornerRadius="6" Padding="12" Margin="0,0,0,10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="💰" FontSize="18" Margin="0,0,10,0"/>
                                            <StackPanel>
                                                <TextBlock Text="总成本" FontSize="12" FontWeight="Bold" Foreground="#92400E"/>
                                                <TextBlock Text="650.8 元/吨" FontSize="16" FontWeight="Bold" Foreground="#F59E0B"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </Border>

                                    <!-- 成本节约 -->
                                    <Border Grid.Row="1" Grid.Column="0" Background="#DCFCE7" CornerRadius="6" Padding="10" Margin="0,0,5,0">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="📉" FontSize="16" HorizontalAlignment="Center"/>
                                            <TextBlock Text="相比上限节约" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#15803D" Margin="0,5,0,0"/>
                                            <TextBlock Text="29.2元/吨" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#22C55E"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- 成本效率 -->
                                    <Border Grid.Row="1" Grid.Column="1" Background="#EEF2FF" CornerRadius="6" Padding="10" Margin="5,0,0,0">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="⚡" FontSize="16" HorizontalAlignment="Center"/>
                                            <TextBlock Text="成本效率" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1E40AF" Margin="0,5,0,0"/>
                                            <TextBlock Text="95.7%" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#2563EB"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- 算法性能 -->
                        <Border Background="White" CornerRadius="8" Padding="15">
                            <StackPanel>
                                <TextBlock Text="⚙️ 算法性能" FontSize="14" FontWeight="Bold" Foreground="#1E40AF" Margin="0,0,0,15"/>
                                
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 算法类型 -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,5,10">
                                        <TextBlock Text="🔬 算法类型" FontSize="11" FontWeight="Bold" Foreground="#475569"/>
                                        <TextBlock Text="SQP二次序列" FontSize="12" FontWeight="Bold" Foreground="#1E40AF" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <!-- 计算精度 -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5,0,0,10">
                                        <TextBlock Text="🎯 计算精度" FontSize="11" FontWeight="Bold" Foreground="#475569"/>
                                        <TextBlock Text="±0.01%" FontSize="12" FontWeight="Bold" Foreground="#22C55E" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <!-- 迭代次数 -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,5,10">
                                        <TextBlock Text="🔄 迭代次数" FontSize="11" FontWeight="Bold" Foreground="#475569"/>
                                        <TextBlock Text="342次" FontSize="12" FontWeight="Bold" Foreground="#F59E0B" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <!-- 收敛时间 -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5,0,0,10">
                                        <TextBlock Text="⏱️ 收敛时间" FontSize="11" FontWeight="Bold" Foreground="#475569"/>
                                        <TextBlock Text="1.2秒" FontSize="12" FontWeight="Bold" Foreground="#8B5CF6" Margin="0,2,0,0"/>
                                    </StackPanel>

                                    <!-- 收敛状态 -->
                                    <Border Grid.Row="2" Grid.ColumnSpan="2" Background="#F0FDF4" CornerRadius="6" Padding="10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <TextBlock Text="✅" FontSize="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="算法成功收敛，所有约束条件满足" FontSize="12" FontWeight="Bold" Foreground="#15803D"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>
</UserControl>