using CommunityToolkit.Mvvm.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace SinterOptimizationClient.Models
{
    /// <summary>
    /// 优化参数模型
    /// </summary>
    public partial class OptimizationParameters : ObservableValidator
    {
        // 优化目标
        [ObservableProperty]
        private OptimizationType optimizationType = OptimizationType.Comprehensive;

        [ObservableProperty]
        private string algorithmType = "质量成本综合优化算法";

        // 成分范围约束
        [ObservableProperty]
        [Range(0, 100)]
        private double tfeMin = 54.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double tfeMax = 56.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double tfeTarget = 55.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double sio2Min = 4.8;

        [ObservableProperty]
        [Range(0, 100)]
        private double sio2Max = 5.3;

        [ObservableProperty]
        [Range(0, 100)]
        private double sio2Target = 5.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double caoMin = 10.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double caoMax = 12.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double caoTarget = 11.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double al2o3Min = 1.5;

        [ObservableProperty]
        [Range(0, 100)]
        private double al2o3Max = 2.5;

        [ObservableProperty]
        [Range(0, 100)]
        private double al2o3Target = 1.89;

        [ObservableProperty]
        [Range(0, 100)]
        private double mgoMin = 1.8;

        [ObservableProperty]
        [Range(0, 100)]
        private double mgoMax = 3.0;

        [ObservableProperty]
        [Range(0, 100)]
        private double mgoTarget = 2.39;

        [ObservableProperty]
        [Range(0, 1)]
        private double pMax = 0.1;

        [ObservableProperty]
        [Range(0, 1)]
        private double sMax = 0.1;

        // 碱度约束
        [ObservableProperty]
        private RoCalculationType roCalculationType = RoCalculationType.CaoSio2;

        [ObservableProperty]
        [Range(0, 10)]
        private double roMin = 1.75;

        [ObservableProperty]
        [Range(0, 10)]
        private double roMax = 2.05;

        [ObservableProperty]
        [Range(0, 10)]
        private double roTarget = 1.90;

        // 成本与质量目标
        [ObservableProperty]
        [Range(0, double.MaxValue)]
        private double costTargetMax = 680.0;

        [ObservableProperty]
        [Range(0, 10)]
        private double tfeMaxDeviation = 0.2;

        [ObservableProperty]
        [Range(0, 1)]
        private double roMaxDeviation = 0.05;

        // 计划日产
        [ObservableProperty]
        [Range(0, double.MaxValue)]
        private double plannedDailyOutput = 6500;

        [ObservableProperty]
        [Range(0, 100)]
        private double metalRecoveryRate = 99;

        [ObservableProperty]
        [Range(0, 100)]
        private double sinterYield = 85;

        [ObservableProperty]
        [Range(0, 100)]
        private double operationRate = 100;

        // 验证方法
        public bool IsValid()
        {
            return TfeMin <= TfeMax &&
                   Sio2Min <= Sio2Max &&
                   CaoMin <= CaoMax &&
                   MgoMin <= MgoMax &&
                   Al2o3Min <= Al2o3Max &&
                   RoMin <= RoMax &&
                   TfeMin >= 0 && TfeMax <= 100 &&
                   Sio2Min >= 0 && Sio2Max <= 100 &&
                   CaoMin >= 0 && CaoMax <= 100 &&
                   MgoMin >= 0 && MgoMax <= 100 &&
                   Al2o3Min >= 0 && Al2o3Max <= 100 &&
                   PMax >= 0 && PMax <= 1 &&
                   SMax >= 0 && SMax <= 1 &&
                   RoMin >= 0 && RoMax <= 10 &&
                   CostTargetMax >= 0 &&
                   TfeMaxDeviation >= 0 &&
                   RoMaxDeviation >= 0 &&
                   PlannedDailyOutput >= 0 &&
                   MetalRecoveryRate >= 0 && MetalRecoveryRate <= 100 &&
                   SinterYield >= 0 && SinterYield <= 100 &&
                   OperationRate >= 0 && OperationRate <= 100;
        }
    }

    /// <summary>
    /// 优化类型枚举
    /// </summary>
    public enum OptimizationType
    {
        CostOptimal,    // 成本最优
        QualityOptimal, // 质量最优
        Comprehensive   // 质量成本综合优化
    }

    /// <summary>
    /// 碱度计算类型枚举
    /// </summary>
    public enum RoCalculationType
    {
        CaoSio2,                    // Ro=CaO/SiO₂
        CaoMgoSio2Al2o3            // Ro=(CaO+MgO)/(SiO₂+Al₂O₃)
    }
}
