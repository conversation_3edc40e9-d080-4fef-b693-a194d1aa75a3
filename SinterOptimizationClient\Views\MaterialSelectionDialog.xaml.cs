using SinterOptimizationClient.ViewModels;
using System.Windows;

namespace SinterOptimizationClient.Views
{
    /// <summary>
    /// MaterialSelectionDialog.xaml 的交互逻辑
    /// </summary>
    public partial class MaterialSelectionDialog : Window
    {
        public MaterialSelectionDialogViewModel ViewModel { get; private set; }

        public MaterialSelectionDialog(MaterialSelectionDialogViewModel viewModel)
        {
            InitializeComponent();
            ViewModel = viewModel;
            DataContext = viewModel;
        }

        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.SelectAllCommand.Execute(null);
        }

        private void SelectNone_Click(object sender, RoutedEventArgs e)
        {
            ViewModel.SelectNoneCommand.Execute(null);
        }

        private void Apply_Click(object sender, RoutedEventArgs e)
        {
            // 验证选择是否可以应用
            if (!ViewModel.CanApplySelection())
            {
                MessageBox.Show(ViewModel.ValidationMessage, "材料选择验证", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 如果有警告，询问用户是否继续
            if (ViewModel.HasValidationWarning)
            {
                var result = MessageBox.Show(
                    $"{ViewModel.ValidationMessage}\n\n是否继续应用当前选择？", 
                    "材料选择提醒", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);
                
                if (result != MessageBoxResult.Yes)
                {
                    return;
                }
            }

            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
