<Window x:Class="SinterOptimizationClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:views="clr-namespace:SinterOptimizationClient.Views"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="烧结智能配料计算系统" 
        Height="900" Width="1600"
        MinHeight="800" MinWidth="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">

    <Grid>
        <Grid.RowDefinitions>
            <!-- 顶部标题栏 -->
            <RowDefinition Height="60"/>
            <!-- 主体区域 -->
            <RowDefinition Height="*"/>
            <!-- 底部版权信息 -->
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>

        <!-- 顶部标题栏 -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}">
            <Grid>
                <TextBlock Text="烧结智能配料计算系统 Sinter Ore Composition &amp; Index Calculation System"
                           FontSize="18"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>

                <!-- 右上角状态指示器 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,20,0">
                    <Border Background="#10B981" CornerRadius="12" Padding="8,4" Margin="0,0,8,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Fill="White" Width="6" Height="6" Margin="0,0,4,0"/>
                            <TextBlock Text="系统运行中" FontSize="10" Foreground="White"/>
                        </StackPanel>
                    </Border>
                    <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='{}{0:yyyy-MM-dd HH:mm}'}"
                               FontSize="11"
                               Foreground="#E5E7EB"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主体区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <!-- 左侧导航栏 -->
                <ColumnDefinition Width="250"/>
                <!-- 主内容区 -->
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航栏 -->
            <Border Grid.Column="0" Background="#2D3748" BorderBrush="#4A5568" BorderThickness="0,0,1,0" Name="NavigationPanel">
                <StackPanel Margin="0,20,0,0">
                    <!-- Logo区域 -->
                    <StackPanel Orientation="Horizontal" Margin="20,0,20,30" HorizontalAlignment="Center">
                        <Border Width="40" Height="40" Background="White" CornerRadius="8" Margin="0,0,10,0">
                            <TextBlock Text="🔥" FontSize="20" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="智能配料"
                                       FontSize="14"
                                       FontWeight="Bold"
                                       Foreground="White"/>
                            <TextBlock Text="计算系统"
                                       FontSize="12"
                                       Foreground="#E5E7EB"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- 导航菜单 -->
                    <StackPanel>
                        <!-- 一级菜单：烧结智能配料 -->
                        <Border Background="#4A5568" Margin="10,0" CornerRadius="6">
                            <StackPanel Orientation="Horizontal" Margin="15,12">
                                <TextBlock Text="⚙️" FontSize="16" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                <TextBlock Text="烧结智能配料" FontSize="14" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- 二级菜单 -->
                        <StackPanel Margin="20,10,10,0">
                            <!-- 优化计算管理 -->
                            <Button Command="{Binding SwitchToOptimizationCommand}"
                                    Margin="0,0,0,8"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,10"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="4"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                          VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#4A5568"/>
                                            </Trigger>
                                            <DataTrigger Binding="{Binding IsOptimizationSelected}" Value="True">
                                                <Setter Property="Background" Value="#F97316"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📊" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="优化计算管理" FontSize="13" Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- 优化结果管理 -->
                            <Button Command="{Binding SwitchToResultCommand}"
                                    HorizontalAlignment="Stretch"
                                    HorizontalContentAlignment="Left">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="15,10"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                            CornerRadius="4"
                                                            Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                          VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#4A5568"/>
                                            </Trigger>
                                            <DataTrigger Binding="{Binding IsResultSelected}" Value="True">
                                                <Setter Property="Background" Value="#F97316"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📈" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="优化结果管理" FontSize="13" Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 主内容区 -->
            <ContentControl Grid.Column="1" Content="{Binding CurrentView}"/>
        </Grid>

        <!-- 底部版权信息 -->
        <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="#E5E7EB" BorderThickness="0,1,0,0">
            <Grid Margin="20,0">
                <TextBlock Text="© 2024 烧结智能配料计算系统 - 版权所有"
                           FontSize="11"
                           Foreground="#6B7280"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>

                <TextBlock Text="技术支持：智能制造实验室"
                           FontSize="10"
                           Foreground="#9CA3AF"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
