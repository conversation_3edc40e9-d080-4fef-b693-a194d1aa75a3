using System;
using System.Threading.Tasks;

namespace SinterOptimizationClient.Services
{
    /// <summary>
    /// 连接测试结果
    /// </summary>
    public class ConnectivityTestResult
    {
        public bool FrontendToBackendSuccess { get; set; }
        public bool BackendToPythonSuccess { get; set; }
        public bool OverallSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public TimeSpan TestDuration { get; set; }
        public DateTime TestTimestamp { get; set; }
        public string BackendVersion { get; set; } = string.Empty;
        public string PythonServiceStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// 连接测试服务接口
    /// </summary>
    public interface IConnectivityTestService
    {
        /// <summary>
        /// 测试完整的连接链路（前端-后端-Python算法）
        /// </summary>
        Task<ConnectivityTestResult> TestFullConnectivityAsync();

        /// <summary>
        /// 测试前端到后端的连接
        /// </summary>
        Task<bool> TestFrontendToBackendAsync();

        /// <summary>
        /// 测试后端到Python算法的连接
        /// </summary>
        Task<bool> TestBackendToPythonAsync();

        /// <summary>
        /// 获取后端服务版本信息
        /// </summary>
        Task<string> GetBackendVersionAsync();

        /// <summary>
        /// 获取Python服务状态
        /// </summary>
        Task<string> GetPythonServiceStatusAsync();
    }
}