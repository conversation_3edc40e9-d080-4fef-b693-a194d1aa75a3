using SinterOptimizationClient.ViewModels;
using System.Collections.Generic;
using System.Linq;

namespace SinterOptimizationClient.Services
{
    /// <summary>
    /// 验证结果类型
    /// </summary>
    public enum ValidationResultType
    {
        Success,
        Warning,
        Error
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public ValidationResultType Type { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool IsValid => Type != ValidationResultType.Error;

        public static ValidationResult Success(string message = "验证通过")
        {
            return new ValidationResult { Type = ValidationResultType.Success, Message = message };
        }

        public static ValidationResult Warning(string message)
        {
            return new ValidationResult { Type = ValidationResultType.Warning, Message = message };
        }

        public static ValidationResult Error(string message)
        {
            return new ValidationResult { Type = ValidationResultType.Error, Message = message };
        }
    }

    /// <summary>
    /// 材料选择验证器
    /// </summary>
    public class MaterialSelectionValidator
    {
        /// <summary>
        /// 验证材料选择是否合理
        /// </summary>
        /// <param name="materials">材料列表</param>
        /// <returns>验证结果</returns>
        public ValidationResult ValidateSelection(IEnumerable<MaterialSelectionItem> materials)
        {
            var selectedMaterials = materials.Where(m => m.IsSelected).ToList();
            
            // 基本数量检查
            if (!selectedMaterials.Any())
                return ValidationResult.Error("请至少选择一种物料进行优化计算");
                
            if (selectedMaterials.Count < 3)
                return ValidationResult.Warning("建议选择至少3种物料以获得更好的优化效果");
            
            // 检查是否有铁矿石（精粉或粗粉）
            var hasIronOre = selectedMaterials.Any(m => m.Category == "精粉" || m.Category == "粗粉");
            if (!hasIronOre)
                return ValidationResult.Error("必须选择至少一种铁矿石（精粉或粗粉）作为主要原料");
            
            // 检查是否有熔剂类材料
            var hasFlux = selectedMaterials.Any(m => m.Category == "添加剂" || m.Category == "渣料");
            if (!hasFlux)
                return ValidationResult.Warning("建议选择熔剂类材料（添加剂或渣料）以调节化学成分");
            
            // 检查是否有燃料
            var hasFuel = selectedMaterials.Any(m => m.Category == "焦粉");
            if (!hasFuel)
                return ValidationResult.Error("必须选择焦粉作为燃料");
            
            // 检查材料类别多样性
            var categoryCount = selectedMaterials.Select(m => m.Category).Distinct().Count();
            if (categoryCount < 3)
                return ValidationResult.Warning("建议选择更多不同类别的材料以提高配料灵活性");
            
            // 检查是否有过多的高价材料
            var expensiveMaterials = selectedMaterials.Where(m => m.Price > 1000).ToList();
            if (expensiveMaterials.Count > selectedMaterials.Count * 0.5)
                return ValidationResult.Warning("选择了较多高价材料，可能会影响成本优化效果");
            
            // 检查是否有返矿或回收料（有助于降低成本）
            var hasRecycled = selectedMaterials.Any(m => m.Category == "返矿" || m.Category == "回收");
            if (!hasRecycled)
                return ValidationResult.Warning("建议选择返矿或回收料以降低生产成本");
            
            return ValidationResult.Success($"材料选择合理，共选择了{selectedMaterials.Count}种物料，涵盖{categoryCount}个类别");
        }

        /// <summary>
        /// 获取选择建议
        /// </summary>
        /// <param name="materials">材料列表</param>
        /// <returns>建议列表</returns>
        public List<string> GetSelectionSuggestions(IEnumerable<MaterialSelectionItem> materials)
        {
            var suggestions = new List<string>();
            var selectedMaterials = materials.Where(m => m.IsSelected).ToList();
            var unselectedMaterials = materials.Where(m => !m.IsSelected).ToList();
            
            // 基于当前选择给出建议
            if (!selectedMaterials.Any(m => m.Category == "精粉"))
            {
                var availableJingFen = unselectedMaterials.Where(m => m.Category == "精粉").ToList();
                if (availableJingFen.Any())
                {
                    suggestions.Add($"建议添加精粉类材料，如：{string.Join("、", availableJingFen.Take(2).Select(m => m.Name))}");
                }
            }
            
            if (!selectedMaterials.Any(m => m.Category == "添加剂"))
            {
                var availableFlux = unselectedMaterials.Where(m => m.Category == "添加剂").ToList();
                if (availableFlux.Any())
                {
                    suggestions.Add($"建议添加熔剂材料，如：{string.Join("、", availableFlux.Take(2).Select(m => m.Name))}");
                }
            }
            
            if (!selectedMaterials.Any(m => m.Category == "回收"))
            {
                var availableRecycled = unselectedMaterials.Where(m => m.Category == "回收" || m.Category == "返矿").ToList();
                if (availableRecycled.Any())
                {
                    suggestions.Add($"建议添加回收材料以降低成本，如：{string.Join("、", availableRecycled.Take(2).Select(m => m.Name))}");
                }
            }
            
            // 成本优化建议
            var avgPrice = selectedMaterials.Where(m => m.Price > 0).Average(m => m.Price);
            if (avgPrice > 800)
            {
                var cheaperOptions = unselectedMaterials.Where(m => m.Price > 0 && m.Price < avgPrice * 0.8).ToList();
                if (cheaperOptions.Any())
                {
                    suggestions.Add($"当前平均价格较高({avgPrice:F0}元/吨)，可考虑添加低成本材料：{string.Join("、", cheaperOptions.Take(2).Select(m => m.Name))}");
                }
            }
            
            return suggestions;
        }
    }
}