using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SinterOptimizationClient.Models
{
    /// <summary>
    /// 优化结果模型
    /// </summary>
    public partial class OptimizationResult : ObservableObject
    {
        [ObservableProperty]
        private bool success;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private string optimizationType = string.Empty;

        [ObservableProperty]
        private Dictionary<string, double> optimalRatios = new();

        [ObservableProperty]
        private SinterProperties sinterProperties = new();

        [ObservableProperty]
        private double unitCost;

        [ObservableProperty]
        private double objectiveValue;

        [ObservableProperty]
        private int iterations;

        [ObservableProperty]
        private string message = string.Empty;

        [ObservableProperty]
        private DateTime timestamp = DateTime.Now;

        [ObservableProperty]
        private OptimizationResult alternativeSolution;

        // 目标值属性（用于偏差计算）
        [ObservableProperty]
        private double targetTFe = 55.0;

        [ObservableProperty]
        private double targetR = 1.90;

        [ObservableProperty]
        private double targetMgO = 2.0;

        [ObservableProperty]
        private double targetAl2O3 = 1.65;

        // 生成结果方案列表
        public List<ResultSolution> GetSolutions()
        {
            var solutions = new List<ResultSolution>();

            if (Success)
            {
                solutions.Add(new ResultSolution
                {
                    SolutionId = 1,
                    OptimizationType = OptimizationType,
                    WetRatios = OptimalRatios,
                    Cost = UnitCost,
                    TFe = SinterProperties.TFe,
                    R = SinterProperties.R,
                    MgO = SinterProperties.MgO,
                    Al2O3 = SinterProperties.Al2O3,
                    TFeDeviation = CalculateTFeDeviation(TargetTFe),
                    RDeviation = CalculateRDeviation(TargetR),
                    ObjectiveValue = ObjectiveValue,
                    Iterations = Iterations
                });

                if (AlternativeSolution?.Success == true)
                {
                    solutions.Add(new ResultSolution
                    {
                        SolutionId = 2,
                        OptimizationType = AlternativeSolution.OptimizationType,
                        WetRatios = AlternativeSolution.OptimalRatios,
                        Cost = AlternativeSolution.UnitCost,
                        TFe = AlternativeSolution.SinterProperties.TFe,
                        R = AlternativeSolution.SinterProperties.R,
                        MgO = AlternativeSolution.SinterProperties.MgO,
                        Al2O3 = AlternativeSolution.SinterProperties.Al2O3,
                        TFeDeviation = AlternativeSolution.CalculateTFeDeviation(TargetTFe),
                        RDeviation = AlternativeSolution.CalculateRDeviation(TargetR),
                        ObjectiveValue = AlternativeSolution.ObjectiveValue,
                        Iterations = AlternativeSolution.Iterations
                    });
                }
            }

            return solutions;
        }



        /// <summary>
        /// 计算TFe偏差值
        /// </summary>
        /// <param name="targetTFe">目标TFe值</param>
        /// <returns>偏差值</returns>
        public double CalculateTFeDeviation(double targetTFe)
        {
            return Math.Abs(SinterProperties.TFe - targetTFe);
        }

        /// <summary>
        /// 计算R偏差值
        /// </summary>
        /// <param name="targetR">目标R值</param>
        /// <returns>偏差值</returns>
        public double CalculateRDeviation(double targetR)
        {
            return Math.Abs(SinterProperties.R - targetR);
        }

        /// <summary>
        /// 计算MgO偏差值
        /// </summary>
        /// <param name="targetMgO">目标MgO值</param>
        /// <returns>偏差值</returns>
        public double CalculateMgODeviation(double targetMgO)
        {
            return Math.Abs(SinterProperties.MgO - targetMgO);
        }

        /// <summary>
        /// 计算Al2O3偏差值
        /// </summary>
        /// <param name="targetAl2O3">目标Al2O3值</param>
        /// <returns>偏差值</returns>
        public double CalculateAl2O3Deviation(double targetAl2O3)
        {
            return Math.Abs(SinterProperties.Al2O3 - targetAl2O3);
        }
    }

    /// <summary>
    /// 烧结矿性质
    /// </summary>
    public partial class SinterProperties : ObservableObject
    {
        [ObservableProperty]
        private double tFe;

        [ObservableProperty]
        private double r;

        [ObservableProperty]
        private double mgO;

        [ObservableProperty]
        private double al2O3;

        [ObservableProperty]
        private double caO;

        [ObservableProperty]
        private double siO2;

        [ObservableProperty]
        private double tiO2;
    }

    /// <summary>
    /// 结果方案
    /// </summary>
    public partial class ResultSolution : ObservableObject
    {
        [ObservableProperty]
        private int solutionId;

        [ObservableProperty]
        private string optimizationType = string.Empty;

        [ObservableProperty]
        private Dictionary<string, double> wetRatios = new();

        [ObservableProperty]
        private double cost;

        [ObservableProperty]
        private double tFe;

        [ObservableProperty]
        private double r;

        [ObservableProperty]
        private double mgO;

        [ObservableProperty]
        private double al2O3;

        [ObservableProperty]
        private double tFeDeviation;

        [ObservableProperty]
        private double rDeviation;

        [ObservableProperty]
        private double objectiveValue;

        [ObservableProperty]
        private int iterations;

        // 格式化湿配比显示 - 以列的形式排列
        public string FormattedWetRatios
        {
            get
            {
                if (WetRatios == null || !WetRatios.Any())
                    return "无数据";

                var ratios = WetRatios
                    .Where(kvp => kvp.Value > 0.01) // 只显示大于0.01%的配比
                    .OrderByDescending(kvp => kvp.Value)
                    .Select(kvp => $"{kvp.Key}: {kvp.Value:F1}%");

                // 以列的形式排列，每行显示一个物料
                return string.Join("\n", ratios);
            }
        }

        // 格式化偏差显示
        public string FormattedTFeDeviation => TFeDeviation >= 0 ? $"+{TFeDeviation:F2}" : $"{TFeDeviation:F2}";
        public string FormattedRDeviation => RDeviation >= 0 ? $"+{RDeviation:F3}" : $"{RDeviation:F3}";
    }

    /// <summary>
    /// 优化请求模型
    /// </summary>
    public class OptimizationRequest
    {
        public List<RawMaterialData> RawMaterials { get; set; } = new();
        public OptimizationTarget Target { get; set; } = new();
        public OptimizationConstraints Constraints { get; set; } = new();
        public string OptimizeType { get; set; } = "cost";
        public bool MultiSolution { get; set; } = true;
    }

    public class RawMaterialData
    {
        public string Name { get; set; } = string.Empty;
        public double Tfe { get; set; }
        public double Cao { get; set; }
        public double Sio2 { get; set; }
        public double Mgo { get; set; }
        public double Al2o3 { get; set; }
        public double H2o { get; set; }
        public double Ig { get; set; }
        public double Price { get; set; }
        public double MinRatio { get; set; }
        public double MaxRatio { get; set; }
    }

    public class OptimizationTarget
    {
        public double TfeTarget { get; set; }
        public double RoTarget { get; set; }
        public double MgoTarget { get; set; }
        public double Al2o3Target { get; set; }
    }

    public class OptimizationConstraints
    {
        public (double Min, double Max) TfeRange { get; set; }
        public (double Min, double Max) RoRange { get; set; }
        public (double Min, double Max) MgoRange { get; set; }
        public (double Min, double Max) Al2o3Range { get; set; }
        public (double Min, double Max) CostRange { get; set; }
    }
}
