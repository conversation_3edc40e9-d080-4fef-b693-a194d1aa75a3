using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using SinterOptimizationClient.Views;
using SinterOptimizationClient.Services;
using SinterOptimizationClient.Models;
using System;
using System.Windows.Controls;

namespace SinterOptimizationClient.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly IOptimizationService _optimizationService;
        private readonly IDataService _dataService;
        private readonly IDialogService _dialogService;
        private readonly IConnectivityTestService _connectivityTestService;

        // 保持ViewModel实例
        private readonly OptimizationViewModel _optimizationViewModel;
        private readonly ResultViewModel _resultViewModel;

        [ObservableProperty]
        private UserControl? currentView;

        [ObservableProperty]
        private bool isOptimizationSelected = true;

        [ObservableProperty]
        private bool isResultSelected = false;

        public MainViewModel(IOptimizationService optimizationService, IDataService dataService, IDialogService dialogService, IConnectivityTestService connectivityTestService = null)
        {
            _optimizationService = optimizationService;
            _dataService = dataService;
            _dialogService = dialogService;
            _connectivityTestService = connectivityTestService;

            // 创建ViewModel实例
            _optimizationViewModel = new OptimizationViewModel(_optimizationService, _dataService, _dialogService, _connectivityTestService);
            _resultViewModel = new ResultViewModel(_dialogService);

            // 订阅优化完成事件
            _optimizationViewModel.OptimizationCompleted += OnOptimizationCompleted;

            // 默认显示优化计算界面
            SwitchToOptimization();
        }

        private void OnOptimizationCompleted(OptimizationResult result)
        {
            // 将结果传递给结果页面
            _resultViewModel.SetOptimizationResult(result);
        }

        [RelayCommand]
        private void SwitchToOptimization()
        {
            CurrentView = new OptimizationView
            {
                DataContext = _optimizationViewModel
            };
            IsOptimizationSelected = true;
            IsResultSelected = false;
        }

        [RelayCommand]
        private void SwitchToResult()
        {
            CurrentView = new ResultView
            {
                DataContext = _resultViewModel
            };
            IsOptimizationSelected = false;
            IsResultSelected = true;
        }
    }
}
