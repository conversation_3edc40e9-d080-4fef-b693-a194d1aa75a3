using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using SinterOptimizationClient.Models;
using SinterOptimizationClient.Services;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;

namespace SinterOptimizationClient.ViewModels
{
    /// <summary>
    /// 物料选择对话框的ViewModel - 增强版支持流式布局
    /// </summary>
    public partial class MaterialSelectionDialogViewModel : ObservableObject
    {
        [ObservableProperty]
        private ObservableCollection<MaterialSelectionItem> leftColumnMaterials = new();

        [ObservableProperty]
        private ObservableCollection<MaterialSelectionItem> rightColumnMaterials = new();

        // 流式布局的统一材料集合
        [ObservableProperty]
        private ObservableCollection<MaterialSelectionItem> allMaterials = new();

        // 流式布局配置
        [ObservableProperty]
        private bool useFlowLayout = true;

        // 选择管理
        [ObservableProperty]
        private int selectedCount;

        [ObservableProperty]
        private string selectionSummary = "未选择任何物料";

        // 连接测试
        [ObservableProperty]
        private bool isConnectivityTestRunning;

        [ObservableProperty]
        private string connectivityStatus = "未测试";

        [ObservableProperty]
        private string connectivityDetails = "";

        // 验证相关
        [ObservableProperty]
        private string validationMessage = "";

        [ObservableProperty]
        private bool hasValidationWarning;

        [ObservableProperty]
        private bool hasValidationError;

        private readonly IConnectivityTestService _connectivityTestService;
        private readonly MaterialSelectionValidator _validator;

        public MaterialSelectionDialogViewModel(ObservableCollection<MaterialData> materials, IConnectivityTestService connectivityTestService = null)
        {
            _connectivityTestService = connectivityTestService;
            _validator = new MaterialSelectionValidator();
            InitializeMaterialLists(materials);
            UpdateSelectionCount();
        }

        private void InitializeMaterialLists(ObservableCollection<MaterialData> materials)
        {
            // 创建材料名称到当前选择状态的映射（基于MaxRatio > 0）
            var currentSelectionState = materials.ToDictionary(m => m.Name, m => m.MaxRatio > 0);

            // 根据需求文档创建物料列表，但使用当前的选择状态
            var leftMaterials = new[]
            {
                new MaterialSelectionItem("碱性精粉", "精粉", GetSelectionState("碱性精粉", currentSelectionState, false), 752.21),
                new MaterialSelectionItem("海瑞", "精粉", GetSelectionState("海瑞", currentSelectionState, false), 822.98),
                new MaterialSelectionItem("巴西粗粉", "粗粉", GetSelectionState("巴西粗粉", currentSelectionState, false), 1473.05),
                new MaterialSelectionItem("高炉返矿", "返矿", GetSelectionState("高炉返矿", currentSelectionState, true), 550.00),
                new MaterialSelectionItem("钢渣", "渣料", GetSelectionState("钢渣", currentSelectionState, true), 550.00),
                new MaterialSelectionItem("生石灰", "添加剂", GetSelectionState("生石灰", currentSelectionState, true), 219.00),
                new MaterialSelectionItem("焦粉", "焦粉", GetSelectionState("焦粉", currentSelectionState, true), 520.00)
            };

            var rightMaterials = new[]
            {
                new MaterialSelectionItem("酸性精粉", "精粉", GetSelectionState("酸性精粉", currentSelectionState, false), 752.21),
                new MaterialSelectionItem("印粉海娜", "精粉", GetSelectionState("印粉海娜", currentSelectionState, true), 822.98),
                new MaterialSelectionItem("俄罗斯精粉", "精粉", GetSelectionState("俄罗斯精粉", currentSelectionState, true), 772.21),
                new MaterialSelectionItem("回收料", "回收", GetSelectionState("回收料", currentSelectionState, true), 100.00),
                new MaterialSelectionItem("氧化铁皮", "铁皮", GetSelectionState("氧化铁皮", currentSelectionState, false), 0),
                new MaterialSelectionItem("轻烧白云石", "添加剂", GetSelectionState("轻烧白云石", currentSelectionState, true), 183.76),
                new MaterialSelectionItem("澳粉纵横", "精粉", GetSelectionState("澳粉纵横", currentSelectionState, true), 832.98)
            };

            LeftColumnMaterials.Clear();
            RightColumnMaterials.Clear();
            AllMaterials.Clear();

            // 填充左右列集合（保持向后兼容）
            foreach (var item in leftMaterials)
            {
                item.PropertyChanged += OnMaterialSelectionChanged;
                LeftColumnMaterials.Add(item);
                AllMaterials.Add(item);
            }

            foreach (var item in rightMaterials)
            {
                item.PropertyChanged += OnMaterialSelectionChanged;
                RightColumnMaterials.Add(item);
                AllMaterials.Add(item);
            }
        }

        /// <summary>
        /// 获取材料的选择状态，优先使用当前状态，如果不存在则使用默认状态
        /// </summary>
        private bool GetSelectionState(string materialName, Dictionary<string, bool> currentState, bool defaultState)
        {
            return currentState.ContainsKey(materialName) ? currentState[materialName] : defaultState;
        }

        private void OnMaterialSelectionChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(MaterialSelectionItem.IsSelected))
            {
                UpdateSelectionCount();
            }
        }

        private void UpdateSelectionCount()
        {
            SelectedCount = AllMaterials.Count(m => m.IsSelected);
            SelectionSummary = SelectedCount > 0 ? $"已选择 {SelectedCount} 种物料" : "未选择任何物料";
            
            // 触发验证
            ValidateSelection();
        }

        private void ValidateSelection()
        {
            var validationResult = _validator.ValidateSelection(AllMaterials);
            
            ValidationMessage = validationResult.Message;
            HasValidationError = validationResult.Type == ValidationResultType.Error;
            HasValidationWarning = validationResult.Type == ValidationResultType.Warning;
        }

        /// <summary>
        /// 获取选择建议
        /// </summary>
        /// <returns>建议列表</returns>
        public List<string> GetSelectionSuggestions()
        {
            return _validator.GetSelectionSuggestions(AllMaterials);
        }

        /// <summary>
        /// 验证选择是否可以应用
        /// </summary>
        /// <returns>是否可以应用</returns>
        public bool CanApplySelection()
        {
            var validationResult = _validator.ValidateSelection(AllMaterials);
            return validationResult.IsValid;
        }

        [RelayCommand]
        private void SelectAll()
        {
            foreach (var item in AllMaterials)
            {
                item.IsSelected = true;
            }
        }

        [RelayCommand]
        private void SelectNone()
        {
            foreach (var item in AllMaterials)
            {
                item.IsSelected = false;
            }
        }

        [RelayCommand]
        private async Task TestConnectivity()
        {
            if (_connectivityTestService == null)
            {
                ConnectivityStatus = "连接测试服务不可用";
                ConnectivityDetails = "连接测试服务未正确初始化";
                return;
            }

            IsConnectivityTestRunning = true;
            ConnectivityStatus = "正在测试连接...";
            ConnectivityDetails = "";

            try
            {
                var result = await _connectivityTestService.TestFullConnectivityAsync();
                
                if (result.OverallSuccess)
                {
                    ConnectivityStatus = "✅ 连接测试通过";
                    ConnectivityDetails = $"测试耗时: {result.TestDuration.TotalMilliseconds:F0}ms\n" +
                                        $"后端版本: {result.BackendVersion}\n" +
                                        $"{result.PythonServiceStatus}";
                }
                else
                {
                    ConnectivityStatus = "❌ 连接测试失败";
                    ConnectivityDetails = result.ErrorMessage;
                }
            }
            catch (System.Exception ex)
            {
                ConnectivityStatus = "❌ 连接测试异常";
                ConnectivityDetails = $"测试过程中发生异常: {ex.Message}";
            }
            finally
            {
                IsConnectivityTestRunning = false;
            }
        }

        public ObservableCollection<MaterialData> GetSelectedMaterials(ObservableCollection<MaterialData> originalMaterials)
        {
            var selectedNames = AllMaterials
                .Where(item => item.IsSelected)
                .Select(item => item.Name)
                .ToHashSet();

            var selectedMaterials = new ObservableCollection<MaterialData>();
            foreach (var material in originalMaterials)
            {
                if (selectedNames.Contains(material.Name))
                {
                    selectedMaterials.Add(material);
                }
            }

            return selectedMaterials;
        }
    }

    /// <summary>
    /// 物料选择项 - 增强版支持流式布局
    /// </summary>
    public partial class MaterialSelectionItem : ObservableObject
    {
        [ObservableProperty]
        private string name;

        [ObservableProperty]
        private string category;

        [ObservableProperty]
        private bool isSelected;

        [ObservableProperty]
        private double price;

        // 流式布局属性
        [ObservableProperty]
        private double itemWidth = 180;

        [ObservableProperty]
        private double itemHeight = 80;

        [ObservableProperty]
        private Thickness itemMargin = new Thickness(5);

        // 视觉样式属性
        [ObservableProperty]
        private Brush categoryBrush;

        [ObservableProperty]
        private Brush categoryBorderBrush;

        [ObservableProperty]
        private Brush backgroundBrush;

        [ObservableProperty]
        private string formattedPrice;

        [ObservableProperty]
        private Visibility priceVisibility;

        public MaterialSelectionItem(string name, string category, bool isSelected, double price)
        {
            this.name = name;
            this.category = category;
            this.isSelected = isSelected;
            this.price = price;
            
            // 初始化增强属性
            InitializeEnhancedProperties();
        }

        private void InitializeEnhancedProperties()
        {
            // 设置格式化价格和可见性
            UpdateFormattedPrice();
            
            // 设置类别样式
            UpdateCategoryStyle();
            
            // 设置背景样式
            UpdateBackgroundStyle();
        }

        partial void OnIsSelectedChanged(bool value)
        {
            UpdateBackgroundStyle();
            OnPropertyChanged(nameof(SelectionStatus));
            OnPropertyChanged(nameof(StatusColor));
        }

        partial void OnPriceChanged(double value)
        {
            UpdateFormattedPrice();
        }

        partial void OnCategoryChanged(string value)
        {
            UpdateCategoryStyle();
        }

        private void UpdateFormattedPrice()
        {
            if (Price > 0)
            {
                FormattedPrice = $"{Price:F2}元/吨";
                PriceVisibility = Visibility.Visible;
            }
            else
            {
                FormattedPrice = "价格待定";
                PriceVisibility = Visibility.Collapsed;
            }
        }

        private void UpdateCategoryStyle()
        {
            var categoryStyles = GetCategoryStyles();
            if (categoryStyles.ContainsKey(Category))
            {
                var style = categoryStyles[Category];
                CategoryBrush = style.Background;
                CategoryBorderBrush = style.Border;
            }
            else
            {
                // 默认样式
                CategoryBrush = new SolidColorBrush(Color.FromRgb(240, 240, 240));
                CategoryBorderBrush = new SolidColorBrush(Color.FromRgb(200, 200, 200));
            }
        }

        private void UpdateBackgroundStyle()
        {
            if (IsSelected)
            {
                BackgroundBrush = new SolidColorBrush(Color.FromRgb(232, 245, 233)); // 浅绿色背景
            }
            else
            {
                BackgroundBrush = new SolidColorBrush(Colors.White);
            }
        }

        private static Dictionary<string, (Brush Background, Brush Border)> GetCategoryStyles()
        {
            return new Dictionary<string, (Brush Background, Brush Border)>
            {
                ["精粉"] = (new SolidColorBrush(Color.FromRgb(227, 242, 253)), new SolidColorBrush(Color.FromRgb(33, 150, 243))),
                ["粗粉"] = (new SolidColorBrush(Color.FromRgb(255, 243, 224)), new SolidColorBrush(Color.FromRgb(255, 152, 0))),
                ["返矿"] = (new SolidColorBrush(Color.FromRgb(232, 245, 232)), new SolidColorBrush(Color.FromRgb(76, 175, 80))),
                ["渣料"] = (new SolidColorBrush(Color.FromRgb(252, 228, 236)), new SolidColorBrush(Color.FromRgb(233, 30, 99))),
                ["添加剂"] = (new SolidColorBrush(Color.FromRgb(243, 229, 245)), new SolidColorBrush(Color.FromRgb(156, 39, 176))),
                ["回收"] = (new SolidColorBrush(Color.FromRgb(224, 242, 241)), new SolidColorBrush(Color.FromRgb(0, 150, 136))),
                ["铁皮"] = (new SolidColorBrush(Color.FromRgb(239, 235, 233)), new SolidColorBrush(Color.FromRgb(121, 85, 72))),
                ["焦粉"] = (new SolidColorBrush(Color.FromRgb(250, 250, 250)), new SolidColorBrush(Color.FromRgb(97, 97, 97)))
            };
        }

        // 保持向后兼容的属性
        public string PriceText => FormattedPrice;

        public string SelectionStatus => IsSelected ? "已选" : "未选";

        public Brush StatusColor => IsSelected ? Brushes.Green : Brushes.Gray;
    }
}
