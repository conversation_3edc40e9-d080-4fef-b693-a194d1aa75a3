<Window x:Class="SinterOptimizationClient.Views.MaterialSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:converters="clr-namespace:SinterOptimizationClient.Converters"
        mc:Ignorable="d"
        Title="物料选择 - 流式布局" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        MinWidth="600" MinHeight="500">

    <Window.Resources>
        <!-- 转换器 -->
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
        
        <!-- 材料卡片样式 -->
        <Style x:Key="MaterialCardStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Width" Value="180"/>
            <Setter Property="Height" Value="80"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 类别标签样式 -->
        <Style x:Key="CategoryLabelStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="4,2"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- 增强的应用选择按钮样式 -->
        <Style x:Key="EnhancedApplyButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="6" 
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#45A049"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3D8B40"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 类别图例样式 -->
        <Style x:Key="LegendItemStyle" TargetType="Border" BasedOn="{StaticResource CategoryLabelStyle}">
            <Setter Property="Margin" Value="3,2"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和说明 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,15">
            <TextBlock Text="物料选择" FontSize="18" FontWeight="Bold" Margin="0,0,0,8"/>
            <TextBlock Text="请选择参与优化计算的物料，不同类别用不同颜色标识" FontSize="12" Foreground="#666" Margin="0,0,0,10"/>
            
            <!-- 类别图例 -->
            <WrapPanel Margin="0,0,0,10">
                <Border Style="{StaticResource LegendItemStyle}" Background="#E3F2FD" BorderBrush="#2196F3">
                    <TextBlock Text="精粉" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource LegendItemStyle}" Background="#FFF3E0" BorderBrush="#FF9800">
                    <TextBlock Text="粗粉" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource LegendItemStyle}" Background="#E8F5E8" BorderBrush="#4CAF50">
                    <TextBlock Text="返矿" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource LegendItemStyle}" Background="#FCE4EC" BorderBrush="#E91E63">
                    <TextBlock Text="渣料" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource LegendItemStyle}" Background="#F3E5F5" BorderBrush="#9C27B0">
                    <TextBlock Text="添加剂" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource LegendItemStyle}" Background="#E0F2F1" BorderBrush="#009688">
                    <TextBlock Text="回收" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource LegendItemStyle}" Background="#EFEBE9" BorderBrush="#795548">
                    <TextBlock Text="铁皮" FontSize="10"/>
                </Border>
                <Border Style="{StaticResource LegendItemStyle}" Background="#FAFAFA" BorderBrush="#616161">
                    <TextBlock Text="焦粉" FontSize="10"/>
                </Border>
            </WrapPanel>
        </StackPanel>

        <!-- 物料列表 - 流式布局 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <ItemsControl ItemsSource="{Binding AllMaterials}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource MaterialCardStyle}" 
                                Background="{Binding BackgroundBrush}"
                                Width="{Binding ItemWidth}" 
                                Height="{Binding ItemHeight}"
                                Margin="{Binding ItemMargin}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                
                                <!-- 顶部：复选框和物料名称 -->
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <CheckBox Grid.Column="0" IsChecked="{Binding IsSelected}" 
                                              VerticalAlignment="Top" Margin="0,0,5,0"/>
                                    <TextBlock Grid.Column="1" Text="{Binding Name}" 
                                               FontWeight="Bold" FontSize="11" 
                                               TextWrapping="Wrap" VerticalAlignment="Top"/>
                                </Grid>
                                
                                <!-- 中部：价格信息 -->
                                <TextBlock Grid.Row="1" Text="{Binding FormattedPrice}" 
                                           FontSize="9" Foreground="#666" 
                                           VerticalAlignment="Center"
                                           Visibility="{Binding PriceVisibility}"/>
                                
                                <!-- 底部：类别标签和状态 -->
                                <Grid Grid.Row="2">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <Border Grid.Column="0" 
                                            Background="{Binding CategoryBrush}"
                                            BorderBrush="{Binding CategoryBorderBrush}"
                                            BorderThickness="1"
                                            CornerRadius="3" 
                                            Padding="3,1" 
                                            HorizontalAlignment="Left">
                                        <TextBlock Text="{Binding Category}" FontSize="8" 
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                    
                                    <Ellipse Grid.Column="1" Width="8" Height="8" 
                                             Fill="{Binding StatusColor}" 
                                             HorizontalAlignment="Right"
                                             VerticalAlignment="Bottom"/>
                                </Grid>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 底部区域：连接测试和按钮 -->
        <Grid Grid.Row="2" Margin="0,15,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 连接测试区域 -->
            <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" 
                    CornerRadius="4" Padding="10" Margin="0,0,0,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 连接状态显示 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="连接状态:" FontSize="12" FontWeight="Medium" Margin="0,0,8,0"/>
                        <TextBlock Text="{Binding ConnectivityStatus}" FontSize="12" FontWeight="Bold"/>
                    </StackPanel>
                    
                    <!-- 连接详情 -->
                    <TextBlock Grid.Column="1" Text="{Binding ConnectivityDetails}" 
                               FontSize="10" Foreground="#666" Margin="15,0,15,0"
                               TextWrapping="Wrap" VerticalAlignment="Center"/>
                    
                    <!-- 测试连接按钮 -->
                    <Button Grid.Column="2" Content="测试连接" Width="80" Height="25" 
                            FontSize="11" Command="{Binding TestConnectivityCommand}"
                            IsEnabled="{Binding IsConnectivityTestRunning, Converter={StaticResource InverseBooleanConverter}}">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#007BFF"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="BorderThickness" Value="0"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="3" BorderThickness="0">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#0056B3"/>
                                                </Trigger>
                                                <Trigger Property="IsEnabled" Value="False">
                                                    <Setter Property="Background" Value="#6C757D"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </Grid>
            </Border>
            
            <!-- 操作按钮 -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right">
                <TextBlock Text="{Binding SelectionSummary}" FontSize="12" Foreground="#666" 
                           VerticalAlignment="Center" Margin="0,0,15,0"/>
                <Button Content="全选" Width="60" Height="30" Margin="0,0,10,0" Click="SelectAll_Click"/>
                <Button Content="全不选" Width="60" Height="30" Margin="0,0,10,0" Click="SelectNone_Click"/>
                <Button Content="应用选择" Width="80" Height="30" Margin="0,0,10,0" 
                        Style="{StaticResource EnhancedApplyButtonStyle}" Click="Apply_Click"/>
                <Button Content="取消" Width="60" Height="30" Click="Cancel_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
